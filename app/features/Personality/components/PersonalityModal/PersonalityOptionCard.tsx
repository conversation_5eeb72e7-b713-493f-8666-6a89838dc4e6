import React, { useState } from 'react';

interface PersonalityOptionCardProps {
  title: string;
  description: string;
  iconSrc: string;
  isSelected?: boolean;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

const PersonalityOptionCard: React.FC<PersonalityOptionCardProps> = ({
  title,
  description,
  iconSrc,
  onClick,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Custom SVG icons matching the Figma design exactly
  const PersonalityIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9 11V14" stroke={isHovered ? "#7F56D9" : "#6B7280"} strokeWidth="1.50" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M11.5 3C7.27 3 5.155 3 3.75 4.18C3.55 4.35 3.37 4.54 3.2 4.75C2 6.155 2 8.27 2 12.5C2 16.73 2 18.845 3.2 20.25C3.37 20.46 3.55 20.65 3.75 20.82C5.155 22 7.27 22 11.5 22C15.73 22 17.845 22 19.25 20.82C19.46 20.65 19.65 20.46 19.82 20.25C21 18.845 21 16.73 21 12.5" stroke={isHovered ? "#7F56D9" : "#6B7280"} strokeWidth="1.50" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 8V17" stroke={isHovered ? "#7F56D9" : "#6B7280"} strokeWidth="1.50" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M15 10V15" stroke={isHovered ? "#7F56D9" : "#6B7280"} strokeWidth="1.50" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M6 12V13" stroke={isHovered ? "#7F56D9" : "#6B7280"} strokeWidth="1.50" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M15.4 5.1C16.8 4.9 17.9 3.8 18.1 2.4C18.1 2.2 18.3 2 18.5 2C18.7 2 18.9 2.2 18.9 2.4C19.1 3.8 20.2 4.9 21.6 5.1C21.8 5.1 22 5.3 22 5.5C22 5.7 21.8 5.9 21.6 5.9C20.2 6.1 19.1 7.2 18.9 8.6C18.9 8.8 18.7 9 18.5 9C18.3 9 18.1 8.8 18.1 8.6C17.9 7.2 16.8 6.1 15.4 5.9C15.2 5.9 15 5.7 15 5.5C15 5.3 15.2 5.1 15.4 5.1Z" stroke={isHovered ? "#7F56D9" : "#6B7280"} strokeWidth="1.50" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const SparklesIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M8.57 3.43C8.95 3.43 9.29 3.68 9.39 4.05L10.32 7.31C10.73 8.73 11.84 9.84 13.26 10.25L16.52 11.18C16.89 11.28 17.14 11.62 17.14 12C17.14 12.38 16.89 12.72 16.52 12.82L13.26 13.75C11.84 14.16 10.73 15.27 10.32 16.69L9.39 19.95C9.29 20.32 8.95 20.57 8.57 20.57C8.19 20.57 7.85 20.32 7.75 19.95L6.82 16.69C6.41 15.27 5.3 14.16 3.88 13.75L0.62 12.82C0.25 12.72 0 12.38 0 12C0 11.62 0.25 11.28 0.62 11.18L3.88 10.25C5.3 9.84 6.41 8.73 6.82 7.31L7.75 4.05C7.85 3.68 8.19 3.43 8.57 3.43Z" fill={isHovered ? "#7F56D9" : "#6B7280"}/>
      <path fillRule="evenodd" clipRule="evenodd" d="M18.86 0C19.24 0 19.59 0.27 19.69 0.65L19.98 1.83C20.25 2.91 21.09 3.75 22.17 4.02L23.35 4.31C23.73 4.41 24 4.76 24 5.14C24 5.52 23.73 5.87 23.35 5.97L22.17 6.26C21.09 6.53 20.25 7.37 19.98 8.45L19.69 9.63C19.59 10.01 19.24 10.28 18.86 10.28C18.48 10.28 18.13 10.01 18.03 9.63L17.74 8.45C17.47 7.37 16.63 6.53 15.55 6.26L14.37 5.97C13.99 5.87 13.72 5.52 13.72 5.14C13.72 4.76 13.99 4.41 14.37 4.31L15.55 4.02C16.63 3.75 17.47 2.91 17.74 1.83L18.03 0.65C18.13 0.27 18.48 0 18.86 0Z" fill={isHovered ? "#7F56D9" : "#6B7280"}/>
      <path fillRule="evenodd" clipRule="evenodd" d="M17.14 15.43C17.51 15.43 17.84 15.68 17.95 16.02L18.41 17.37C18.58 17.89 18.97 18.28 19.49 18.45L20.84 18.91C21.18 19.02 21.43 19.35 21.43 19.72C21.43 20.09 21.18 20.42 20.84 20.53L19.49 20.99C18.97 21.16 18.58 21.55 18.41 22.07L17.95 23.42C17.84 23.76 17.51 24.01 17.14 24.01C16.77 24.01 16.44 23.76 16.33 23.42L15.87 22.07C15.7 21.55 15.31 21.16 14.79 20.99L13.44 20.53C13.1 20.42 12.85 20.09 12.85 19.72C12.85 19.35 13.1 19.02 13.44 18.91L14.79 18.45C15.31 18.28 15.7 17.89 15.87 17.37L16.33 16.02C16.44 15.68 16.77 15.43 17.14 15.43Z" fill={isHovered ? "#7F56D9" : "#6B7280"}/>
    </svg>
  );

  const getIcon = () => {
    if (iconSrc.includes('personality-icon')) {
      return <PersonalityIcon />;
    } else if (iconSrc.includes('sparkles')) {
      return <SparklesIcon />;
    }
    return null;
  };

  return (
    <div
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`
        w-full px-3.5 py-3 bg-white rounded-[20px]
        outline outline-1 outline-offset-[-1px]
        inline-flex justify-start items-center gap-2 overflow-hidden
        cursor-pointer transition-all duration-200 ease-in-out
        ${isHovered
          ? 'outline-violet-500 shadow-[0px_0px_12px_0px_rgba(127,86,217,0.30)]'
          : 'outline-neutral-200'
        }
      `}
    >
      <div className="flex-1 inline-flex flex-col justify-center items-start gap-1">
        <div className={`
          p-2 rounded-xl inline-flex justify-start items-center gap-2.5 transition-all duration-200
          ${isHovered ? 'bg-violet-500/10' : 'bg-neutral-100'}
        `}>
          {getIcon()}
        </div>
        <div className="justify-start text-zinc-900 text-base font-semibold font-['Plus_Jakarta_Sans'] leading-normal">
          {title}
        </div>
        <div className="self-stretch justify-start text-zinc-900 text-sm font-normal font-['Plus_Jakarta_Sans'] leading-normal">
          {description}
        </div>
      </div>
    </div>
  );
};

export default PersonalityOptionCard;
